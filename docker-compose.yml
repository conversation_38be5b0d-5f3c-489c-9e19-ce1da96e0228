services:
  app:
    container_name: openai-edge-tts
    restart: unless-stopped
    build:
      context: .
      args:
        INSTALL_FFMPEG: ${INSTALL_FFMPEG_ARG:-false} # Controlled by env var or defaults to false
    ports:
      - '${PORT:-5050}:5050'
    env_file:
      - .env
    environment: # optionally define in -e argument when running docker command
      API_KEY: ${API_KEY:-your_api_key_here}
      PORT: ${PORT:-5050}
      DEFAULT_VOICE: ${DEFAULT_VOICE:-en-US-AvaNeural}
      DEFAULT_RESPONSE_FORMAT: ${DEFAULT_RESPONSE_FORMAT:-mp3}
      DEFAULT_SPEED: ${DEFAULT_SPEED:-1.0}
      DEFAULT_LANGUAGE: ${DEFAULT_LANGUAGE:-en-US}
      REQUIRE_API_KEY: ${REQUIRE_API_KEY:-True}
      REMOVE_FILTER: ${REMOVE_FILTER:-False}
      EXPAND_API: ${EXPAND_API:-True}
      DETAILED_ERROR_LOGGING: ${DETAILED_ERROR_LOGGING:-True}
